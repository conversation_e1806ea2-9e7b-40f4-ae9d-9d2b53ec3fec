import React, { useState, useEffect } from "react";
import { Plus, CheckCircle2 } from "lucide-react";
import { Task } from "../types";
import { firestoreUserTasks } from "../utils/firestoreUserTasks";
import { realTimeAuth } from "../utils/realTimeAuth";
import { isAfter, startOfDay } from "date-fns";
import { SwipeableTaskItem } from "./SwipeableTaskItem";

export const TaskManager: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [showAddTask, setShowAddTask] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [filter, setFilter] = useState<
    "all" | "pending" | "completed" | "overdue"
  >("all");
  const [taskForm, setTaskForm] = useState({
    title: "",
    description: "",
    subject: "",
    dueDate: "",
    priority: "medium" as "low" | "medium" | "high",
  });

  const user = realTimeAuth.getCurrentUser();

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <h2 className="text-2xl font-bold mb-4">
          Please log in to access your tasks.
        </h2>
        <p className="text-gray-600 mb-6">
          Sign in to create, view, and manage your personal cloud tasks.
        </p>
      </div>
    );
  }

  useEffect(() => {
    loadTasks();
  }, [user]);

  const loadTasks = async () => {
    try {
      const userTasks = await firestoreUserTasks.getTasks(user.id);
      setTasks(
        userTasks.sort(
          (a, b) =>
            new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
        )
      );
    } catch (error) {
      console.error("Error loading tasks:", error);
    }
  };

  const resetForm = () => {
    setTaskForm({
      title: "",
      description: "",
      subject: "",
      dueDate: "",
      priority: "medium",
    });
  };

  const handleAddTask = async () => {
    if (!taskForm.title.trim()) {
      alert("Title is required.");
      return;
    }
    if (!taskForm.dueDate) {
      alert("Due Date is required.");
      return;
    }

    const newTask = {
      title: taskForm.title.trim(),
      description: taskForm.description.trim(),
      subject: taskForm.subject.trim(),
      dueDate: taskForm.dueDate,
      priority: taskForm.priority,
      status: "pending" as "pending" | "completed",
      createdAt: new Date().toISOString(),
    };

    try {
      await firestoreUserTasks.addTask(user.id, newTask);
      resetForm();
      setShowAddTask(false);
      await loadTasks();
    } catch (error) {
      console.error("Error adding task:", error);
      alert("Failed to add task. Please try again.");
    }
  };

  const handleEditTask = async () => {
    if (!editingTask) return;

    if (!taskForm.title.trim()) {
      alert("Title is required.");
      return;
    }
    if (!taskForm.dueDate) {
      alert("Due Date is required.");
      return;
    }

    const updates = {
      title: taskForm.title.trim(),
      description: taskForm.description.trim(),
      subject: taskForm.subject.trim(),
      dueDate: taskForm.dueDate,
      priority: taskForm.priority,
    };

    try {
      await firestoreUserTasks.updateTask(user.id, editingTask.id, updates);
      setEditingTask(null);
      resetForm();
      await loadTasks();
    } catch (error) {
      console.error("Error updating task:", error);
      alert("Failed to update task. Please try again.");
    }
  };

  const toggleTaskStatus = async (task: Task) => {
    try {
      await firestoreUserTasks.updateTask(user.id, task.id, {
        status: task.status === "completed" ? "pending" : "completed",
      });
      await loadTasks();
    } catch (error) {
      console.error("Error toggling task status:", error);
      alert("Failed to update task status. Please try again.");
    }
  };

  const deleteTask = async (taskId: string) => {
    try {
      await firestoreUserTasks.deleteTask(user.id, taskId);
      await loadTasks();
    } catch (error) {
      console.error("Error deleting task:", error);
      alert("Failed to delete task. Please try again.");
    }
  };

  const startEditing = (task: Task) => {
    setEditingTask(task);
    setTaskForm({
      title: task.title,
      description: task.description,
      subject: task.subject,
      dueDate: task.dueDate,
      priority: task.priority,
    });
  };

  const isOverdue = (task: Task) => {
    if (task.status === "completed") return false;
    return isAfter(startOfDay(new Date()), startOfDay(new Date(task.dueDate)));
  };

  const getFilteredTasks = () => {
    switch (filter) {
      case "pending":
        return tasks.filter((task) => task.status === "pending");
      case "completed":
        return tasks.filter((task) => task.status === "completed");
      case "overdue":
        return tasks.filter((task) => isOverdue(task));
      default:
        return tasks;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600 bg-red-50";
      case "medium":
        return "text-yellow-600 bg-yellow-50";
      case "low":
        return "text-green-600 bg-green-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  const getStatusCounts = () => {
    return {
      all: tasks.length,
      pending: tasks.filter((t) => t.status === "pending").length,
      completed: tasks.filter((t) => t.status === "completed").length,
      overdue: tasks.filter((t) => isOverdue(t)).length,
    };
  };

  const counts = getStatusCounts();

  return (
    <div className="bg-white h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">To-Do List</h2>
          <button
            onClick={() => setShowAddTask(true)}
            className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Task
          </button>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { key: "all", label: "All", count: counts.all },
            { key: "pending", label: "Pending", count: counts.pending },
            { key: "completed", label: "Completed", count: counts.completed },
            { key: "overdue", label: "Overdue", count: counts.overdue },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as any)}
              className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                filter === tab.key
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {tab.label}
              <span
                className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                  tab.key === "overdue" && tab.count > 0
                    ? "bg-red-100 text-red-600"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                {tab.count}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Task List */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-4">
          {getFilteredTasks().map((task) => (
            <SwipeableTaskItem
              key={task.id}
              task={task}
              onToggleStatus={toggleTaskStatus}
              onEdit={startEditing}
              onDelete={deleteTask}
              getPriorityColor={getPriorityColor}
            />
          ))}
        </div>

        {getFilteredTasks().length === 0 && (
          <div className="text-center py-12">
            <CheckCircle2 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filter === "all" ? "No tasks yet" : `No ${filter} tasks`}
            </h3>
            <p className="text-gray-600 mb-6">
              {filter === "all"
                ? "Create your first task to get started with organized studying"
                : `You don't have any ${filter} tasks at the moment`}
            </p>
            {filter === "all" && (
              <button
                onClick={() => setShowAddTask(true)}
                className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Plus className="w-5 h-5 mr-2" />
                Add Your First Task
              </button>
            )}
          </div>
        )}
      </div>

      {/* Add/Edit Task Modal */}
      {(showAddTask || editingTask) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-lg">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold">
                {editingTask ? "Edit Task" : "Add New Task"}
              </h3>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  value={taskForm.title}
                  onChange={(e) =>
                    setTaskForm({ ...taskForm, title: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter task title"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={taskForm.description}
                  onChange={(e) =>
                    setTaskForm({ ...taskForm, description: e.target.value })
                  }
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter task description"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subject
                  </label>
                  <input
                    type="text"
                    value={taskForm.subject}
                    onChange={(e) =>
                      setTaskForm({ ...taskForm, subject: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Mathematics"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority
                  </label>
                  <select
                    value={taskForm.priority}
                    onChange={(e) =>
                      setTaskForm({
                        ...taskForm,
                        priority: e.target.value as any,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Due Date *
                </label>
                <input
                  type="date"
                  value={taskForm.dueDate}
                  onChange={(e) =>
                    setTaskForm({ ...taskForm, dueDate: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                onClick={() => {
                  if (editingTask) {
                    setEditingTask(null);
                  } else {
                    setShowAddTask(false);
                  }
                  resetForm();
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={editingTask ? handleEditTask : handleAddTask}
                disabled={!taskForm.title.trim() || !taskForm.dueDate}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {editingTask ? "Update" : "Add"} Task
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
