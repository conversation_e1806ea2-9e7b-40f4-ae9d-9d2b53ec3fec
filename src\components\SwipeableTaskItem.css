/* Additional styles for swipeable task items */
.swipeable-task-item {
  touch-action: pan-y; /* Allow vertical scrolling but handle horizontal swipes */
  user-select: none; /* Prevent text selection during swipe */
}

.swipeable-task-item:active {
  cursor: grabbing;
}

/* Smooth transitions for swipe actions */
.swipe-action-bg {
  transition: opacity 0.2s ease-out;
}

/* Pulse animation for action feedback */
@keyframes swipe-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.swipe-action-active {
  animation: swipe-pulse 0.6s ease-in-out;
}

/* Prevent text selection during drag */
.swipeable-task-item * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Re-enable text selection for content when not dragging */
.swipeable-task-item:not(.dragging) .task-content {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
